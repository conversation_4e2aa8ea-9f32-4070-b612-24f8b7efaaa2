import axios, { CancelTokenSource } from 'axios';

import { API_ENDPOINT } from '../constants/api-endpoints';
import { getToken } from '../utils/authUtils';
import { cognitoStorageKeys } from '../utils/cognitoConfig';

// Custom error class for cancelled requests
class RequestCancelledError extends Error {
  constructor() {
    super('Request was cancelled');
    this.name = 'RequestCancelledError';
  }
}

declare module 'axios' {
  export interface AxiosRequestConfig {
    cancelDuplicateRequest?: boolean;
    _isCancelled?: boolean;
  }
}

// Map to store pending requests
const pendingRequests = new Map<string, CancelTokenSource>();

/**
 * Generate a unique key for each request based on method and URL
 */
const generateRequestKey = (config: any): string => {
  const { method, url, params, data } = config;
  // For POST/PUT requests, include a hash of the request data to differentiate between different payloads
  const dataKey = ['POST', 'PUT', 'PATCH'].includes(method?.toUpperCase())
    ? JSON.stringify(data)
    : '';
  return `${method?.toLowerCase()}:${url}?${new URLSearchParams(params).toString()}:${dataKey}`;
};

/**
 * Cancel duplicate pending requests
 */
const cancelPendingRequest = (key: string) => {
  if (pendingRequests.has(key)) {
    const source = pendingRequests.get(key);
    // Mark the request as cancelled before cancelling
    if (source?.token) {
      (source.token as any)._isCancelled = true;
    }
    source?.cancel();
    pendingRequests.delete(key);
  }
};

const api = axios.create({
  baseURL: API_ENDPOINT,
  headers: {
    'Content-Type': 'application/json',
    // 'Ocp-Apim-Subscription-Key': SUBSCRIPTION_KEY,
  },
  timeout: 60000, // 60 seconds timeout
});

// Request interceptor to add auth token and handle token refresh
api.interceptors.request.use(
  async (config) => {
    // Skip token check for login/refresh token endpoints
    if (config.url?.includes('/auth/') || config.url?.includes('/login')) {
      return config;
    }

    // Generate a unique key for the request
    const requestKey = generateRequestKey(config);

    // If this is a duplicate request, cancel the previous one
    if (config.cancelDuplicateRequest !== false) {
      cancelPendingRequest(requestKey);

      // Create a new cancel token for this request
      const source = axios.CancelToken.source();
      config.cancelToken = source.token;

      // Store the cancel token
      pendingRequests.set(requestKey, source);
    }

    try {
      // Get a fresh token for each request
      const token = await getToken();

      if (token) {
        // Ensure the token is properly formatted
        const formattedToken = token.startsWith('Bearer ')
          ? token
          : `Bearer ${token}`;
        config.headers.Authorization = formattedToken;
      } else {
        console.warn('No token available for request:', config.url);
      }

      return config;
    } catch (error) {
      console.error('Error setting auth token:', error);
      // Clear auth state and redirect to login on token error
      localStorage.removeItem('token');
      sessionStorage.removeItem('msal.accessToken');
      window.location.href = '/login';
      return Promise.reject(error);
    }
  },
  (error) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor to handle responses and clean up pending requests
api.interceptors.response.use(
  (response) => {
    // Clean up the pending request
    const requestKey = generateRequestKey(response.config);
    pendingRequests.delete(requestKey);

    // Check for subscription expiry message in response
    const checkSubscriptionExpiry = (data: any): string | null => {
      // Check if data is a string
      if (typeof data === 'string') {
        if (
          data.includes('Your subscription expired. Need to upgrade.') ||
          data.toLowerCase().includes('subscription expired')
        ) {
          return data;
        }
      }

      // Check if data is an object with message property
      if (data && typeof data === 'object') {
        // Check various possible message fields
        const messageFields = [
          'message',
          'error',
          'errorMessage',
          'msg',
          'data',
        ];
        for (const field of messageFields) {
          const fieldValue = data[field];
          if (typeof fieldValue === 'string') {
            if (
              fieldValue.includes(
                'Your subscription expired. Need to upgrade.'
              ) ||
              fieldValue.toLowerCase().includes('subscription expired')
            ) {
              return fieldValue;
            }
          }
        }

        // Check if the entire response data is the subscription message
        const dataString = JSON.stringify(data);
        if (
          dataString.includes('Your subscription expired. Need to upgrade.') ||
          dataString.toLowerCase().includes('subscription expired')
        ) {
          return typeof data === 'string' ? data : dataString;
        }
      }

      return null;
    };

    // Debug: Log ALL responses to see what we're getting
    console.log('API Response URL:', response.config.url);
    console.log('API Response Data:', response.data);
    console.log('API Response Data Type:', typeof response.data);
    console.log('API Response Status:', response.status);

    const subscriptionMessage = checkSubscriptionExpiry(response.data);
    console.log('Subscription check result:', subscriptionMessage);

    if (subscriptionMessage) {
      console.log('SUBSCRIPTION EXPIRED DETECTED! Redirecting...');

      // Redirect to unauthorized page with subscription_expired reason
      if (typeof window !== 'undefined') {
        // Use setTimeout to ensure the redirect happens after the current execution
        setTimeout(() => {
          console.log('Executing redirect now...');
          window.location.href = '/unauthorized?reason=subscription_expired';
        }, 0);
      }

      // Return a rejected promise with a specific error to prevent further processing
      return Promise.reject({
        isSubscriptionExpired: true,
        message: subscriptionMessage,
        skipToast: true, // Flag to skip showing toast for this error
      });
    }

    return response;
  },
  async (error) => {
    // Check if this is a cancelled request
    if (axios.isCancel(error) || error?.config?._isCancelled) {
      // Return a special error that won't trigger toast
      return Promise.reject(new RequestCancelledError());
    }

    // Check for subscription expiry message in error response
    const checkSubscriptionExpiryInError = (errorData: any): string | null => {
      if (!errorData) return null;

      // Check error response data
      if (errorData.response?.data) {
        const data = errorData.response.data;

        // Check if data is a string
        if (typeof data === 'string') {
          if (
            data.includes('Your subscription expired. Need to upgrade.') ||
            data.toLowerCase().includes('subscription expired')
          ) {
            return data;
          }
        }

        // Check if data is an object with message property
        if (data && typeof data === 'object') {
          const messageFields = ['message', 'error', 'errorMessage', 'msg'];
          for (const field of messageFields) {
            const fieldValue = data[field];
            if (typeof fieldValue === 'string') {
              if (
                fieldValue.includes(
                  'Your subscription expired. Need to upgrade.'
                ) ||
                fieldValue.toLowerCase().includes('subscription expired')
              ) {
                return fieldValue;
              }
            }
          }
        }
      }

      // Check error message directly
      if (typeof errorData.message === 'string') {
        if (
          errorData.message.includes(
            'Your subscription expired. Need to upgrade.'
          ) ||
          errorData.message.toLowerCase().includes('subscription expired')
        ) {
          return errorData.message;
        }
      }

      return null;
    };

    // Debug: Log ALL errors to see what we're getting

    const subscriptionMessage = checkSubscriptionExpiryInError(error);
    console.log('Error subscription check result:', subscriptionMessage);

    if (subscriptionMessage) {
      console.log('SUBSCRIPTION EXPIRED DETECTED IN ERROR! Redirecting...');

      // Redirect to unauthorized page with subscription_expired reason
      if (typeof window !== 'undefined') {
        setTimeout(() => {
          console.log('Executing redirect from error now...');
          window.location.href = '/unauthorized?reason=subscription_expired';
        }, 0);
      }

      // Return a rejected promise with a specific error to prevent further processing
      return Promise.reject({
        isSubscriptionExpired: true,
        message: subscriptionMessage,
        skipToast: true,
      });
    }

    const originalRequest = error.config;

    // Clean up the pending request on error
    if (originalRequest) {
      const requestKey = generateRequestKey(originalRequest);
      pendingRequests.delete(requestKey);
    }

    // Log the error for debugging (except for cancelled requests)
    if (!axios.isCancel(error)) {
      console.error('API Error:', {
        url: originalRequest?.url,
        status: error.response?.status,
        message: error.message,
        response: error.response?.data,
      });
    }

    // Handle 401 Unauthorized
    if (error.response?.status === 401) {
      console.warn('API 401 Unauthorized encountered. Redirecting to unauthorized page.');

      // Clear all auth-related data
      localStorage.removeItem('token');
      sessionStorage.removeItem(cognitoStorageKeys.accessToken);
      sessionStorage.removeItem(cognitoStorageKeys.idToken);
      sessionStorage.removeItem(cognitoStorageKeys.refreshToken);
      sessionStorage.removeItem(cognitoStorageKeys.authProvider);

      if (typeof window !== 'undefined') {
        const currentPath = window.location.pathname;
        if (currentPath !== '/unauthorized' && currentPath !== '/login') {
          // Store the current URL to potentially redirect back later
          sessionStorage.setItem('preLoginUrl', currentPath);
          window.location.href = '/unauthorized?reason=unauthorized';
        }
      }
      return Promise.reject(error);
    }

    return Promise.reject(error);
  }
);

export default api;
