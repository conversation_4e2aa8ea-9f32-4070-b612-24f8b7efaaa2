/**
 * Storage Utilities
 * Provides clear separation between localStorage and sessionStorage usage
 * to prevent user data sharing across tabs
 */

// ==================== SESSION STORAGE (Tab-specific) ====================
// Use for user-specific data that should NOT be shared across tabs

export const SessionStorageKeys = {
  // User-specific data
  USER: 'user',
  EMR_USER_INFO: 'emrUserInfo', 
  USER_ROLE: 'userRole',
  
  // Authentication tokens (already in sessionStorage via Cognito)
  // These are handled by cognitoStorageKeys in cognitoConfig.ts
} as const;

/**
 * Store user data in sessionStorage (tab-specific)
 */
export const setUserData = (key: keyof typeof SessionStorageKeys, data: any): void => {
  try {
    sessionStorage.setItem(SessionStorageKeys[key], JSON.stringify(data));
  } catch (error) {
    console.error(`Failed to store ${key} in sessionStorage:`, error);
  }
};

/**
 * Get user data from sessionStorage (tab-specific)
 */
export const getUserData = <T = any>(key: keyof typeof SessionStorageKeys): T | null => {
  try {
    const data = sessionStorage.getItem(SessionStorageKeys[key]);
    return data ? JSON.parse(data) : null;
  } catch (error) {
    console.error(`Failed to get ${key} from sessionStorage:`, error);
    return null;
  }
};

/**
 * Remove user data from sessionStorage
 */
export const removeUserData = (key: keyof typeof SessionStorageKeys): void => {
  try {
    sessionStorage.removeItem(SessionStorageKeys[key]);
  } catch (error) {
    console.error(`Failed to remove ${key} from sessionStorage:`, error);
  }
};

/**
 * Clear all user data from sessionStorage
 */
export const clearAllUserData = (): void => {
  Object.values(SessionStorageKeys).forEach(key => {
    try {
      sessionStorage.removeItem(key);
    } catch (error) {
      console.error(`Failed to remove ${key} from sessionStorage:`, error);
    }
  });
};

// ==================== LOCAL STORAGE (Cross-tab persistent) ====================
// Use for application-wide settings that should persist across tabs and sessions

export const LocalStorageKeys = {
  // Application settings (can be shared across tabs)
  SELECTED_ORGANIZATION: 'selectedOrganization', // Only for super admins
  
  // Theme/UI preferences (if any)
  // THEME_PREFERENCE: 'themePreference',
  // LANGUAGE_PREFERENCE: 'languagePreference',
  
  // Non-sensitive application state
  // LAST_VISITED_PAGE: 'lastVisitedPage',
} as const;

/**
 * Store application data in localStorage (cross-tab persistent)
 */
export const setAppData = (key: keyof typeof LocalStorageKeys, data: any): void => {
  try {
    localStorage.setItem(LocalStorageKeys[key], JSON.stringify(data));
  } catch (error) {
    console.error(`Failed to store ${key} in localStorage:`, error);
  }
};

/**
 * Get application data from localStorage (cross-tab persistent)
 */
export const getAppData = <T = any>(key: keyof typeof LocalStorageKeys): T | null => {
  try {
    const data = localStorage.getItem(LocalStorageKeys[key]);
    return data ? JSON.parse(data) : null;
  } catch (error) {
    console.error(`Failed to get ${key} from localStorage:`, error);
    return null;
  }
};

/**
 * Remove application data from localStorage
 */
export const removeAppData = (key: keyof typeof LocalStorageKeys): void => {
  try {
    localStorage.removeItem(LocalStorageKeys[key]);
  } catch (error) {
    console.error(`Failed to remove ${key} from localStorage:`, error);
  }
};

// ==================== MIGRATION UTILITIES ====================

/**
 * Migrate user data from localStorage to sessionStorage
 * This should be called once during app initialization
 */
export const migrateUserDataToSessionStorage = (): void => {
  console.log('Migrating user data from localStorage to sessionStorage...');
  
  // Migrate user data
  const userData = localStorage.getItem('user');
  if (userData) {
    sessionStorage.setItem(SessionStorageKeys.USER, userData);
    localStorage.removeItem('user');
    console.log('Migrated user data to sessionStorage');
  }
  
  // Migrate EMR user info
  const emrUserInfo = localStorage.getItem('emrUserInfo');
  if (emrUserInfo) {
    sessionStorage.setItem(SessionStorageKeys.EMR_USER_INFO, emrUserInfo);
    localStorage.removeItem('emrUserInfo');
    console.log('Migrated EMR user info to sessionStorage');
  }
  
  // Migrate user role
  const userRole = localStorage.getItem('userRole');
  if (userRole) {
    sessionStorage.setItem(SessionStorageKeys.USER_ROLE, userRole);
    localStorage.removeItem('userRole');
    console.log('Migrated user role to sessionStorage');
  }
  
  // Note: selectedOrganization stays in localStorage as it's for super admins
  // and should persist across tabs
  
  console.log('User data migration completed');
};

/**
 * Check if user is authenticated (has user data in sessionStorage)
 */
export const isUserDataAvailable = (): boolean => {
  return !!sessionStorage.getItem(SessionStorageKeys.USER);
};

/**
 * Get current user from sessionStorage
 */
export const getCurrentUser = () => {
  return getUserData('USER');
};

/**
 * Get current EMR user info from sessionStorage
 */
export const getCurrentEmrUserInfo = () => {
  return getUserData('EMR_USER_INFO');
};

/**
 * Get current user role from sessionStorage
 */
export const getCurrentUserRole = (): string | null => {
  return getUserData('USER_ROLE');
};
