import CloseIcon from '@mui/icons-material/Close';
import { format } from 'date-fns';
import React, { useEffect, useMemo, useState } from 'react';

import SearchBar from '../../../components/Common/SearchBar';
import DatePicker from '../../../components/DatePicker';
import invoiceService, {
  InvoiceDetailResponse,
  PatientProfileResponse
} from '../../../store/features/invoices/invoice.service';
import { NormalizedInvoice } from '../../../store/features/invoices/invoice.slice';
import organizationService from '../../../store/features/organizations/organization.service';

interface ConsultationRow {
  id: string;
  date: string;
  employeeId: string;
  department: string;
  consultationFee: number;
}

interface PatientProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
  invoiceDetail: InvoiceDetailResponse | null;
  invoices: NormalizedInvoice[];
  loading?: boolean;
}

const PatientProfileModal: React.FC<PatientProfileModalProps> = ({
  isOpen,
  onClose,
  invoiceDetail,
  invoices: _invoices,
  loading = false,
}) => {
  const [searchText, setSearchText] = useState('');
  const [fromDate, setFromDate] = useState<string>('');
  const [toDate, setToDate] = useState<string>('');
  const [organizationName, setOrganizationName] = useState<string>('-');
  const [patientProfile, setPatientProfile] =
    useState<PatientProfileResponse | null>(null);
  const [profileLoading, setProfileLoading] = useState(false);

  // Lock body scroll when modal is open
  useEffect(() => {
    if (!isOpen) return;

    const body = document.body;
    const html = document.documentElement;
    const appRoot = document.getElementById('root');

    const originalBodyOverflow = body.style.overflow;
    const originalHtmlOverflow = html.style.overflow;
    const originalRootOverflow = appRoot?.style.overflow;

    body.style.overflow = 'hidden';
    html.style.overflow = 'hidden';
    if (appRoot) {
      appRoot.style.overflow = 'hidden';
    }

    return () => {
      body.style.overflow = originalBodyOverflow;
      html.style.overflow = originalHtmlOverflow;
      if (appRoot) {
        appRoot.style.overflow = originalRootOverflow ?? '';
      }
    };
  }, [isOpen]);

  // Get patient info from invoice detail
  const patient = invoiceDetail?.patient;
  const patientId = invoiceDetail?.patientId || patient?.id || '';
  const patientName = patient?.name || invoiceDetail?.fullName || '';
  const patientDob = patient?.dob || '';
  const patientAge = patient?.age;
  const patientAbha = patient?.proof?.abhaNumber || '-';

  // Fetch organization name when invoice detail changes
  useEffect(() => {
    let isMounted = true;

    const loadOrganizationName = async () => {
      const organizationId = invoiceDetail?.organizationId;

      if (!isOpen || !organizationId) {
        if (isMounted) {
          setOrganizationName('-');
        }
        return;
      }

      try {
        const organization =
          await organizationService.fetchOrganizationById(organizationId);
        if (isMounted) {
          setOrganizationName(organization?.name || '-');
        }
      } catch (error) {
        console.error('Failed to fetch organization name', error);
        if (isMounted) {
          setOrganizationName('-');
        }
      }
    };

    loadOrganizationName();

    return () => {
      isMounted = false;
    };
  }, [invoiceDetail?.organizationId, isOpen]);

  // Fetch patient profile and consultations (without filters - filtering done client-side)
  useEffect(() => {
    let isMounted = true;

    const loadPatientProfile = async () => {
      const organizationId = invoiceDetail?.organizationId;
      const patientIdValue = patientId;

      if (!isOpen || !organizationId || !patientIdValue) {
        if (isMounted) {
          setPatientProfile(null);
        }
        return;
      }

      setProfileLoading(true);
      try {
        const profileData = await invoiceService.fetchPatientProfile({
          patientId: patientIdValue,
          organizationId,
          // No filter parameters - filtering done client-side
        });
        if (isMounted) {
          setPatientProfile(profileData);
        }
      } catch (error) {
        console.error('Failed to fetch patient profile', error);
        if (isMounted) {
          setPatientProfile(null);
        }
      } finally {
        if (isMounted) {
          setProfileLoading(false);
        }
      }
    };

    loadPatientProfile();

    return () => {
      isMounted = false;
    };
  }, [invoiceDetail?.organizationId, patientId, isOpen]);

  // Format date for display
  const formatDisplayDate = (date: Date | string | null): string => {
    if (!date) return '-';
    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      if (Number.isNaN(dateObj.getTime())) return '-';
      return format(dateObj, 'dd-MM-yyyy');
    } catch {
      return '-';
    }
  };

  // Calculate age
  const getAge = (dob?: string): number | string => {
    if (!dob || dob === 'yyyy-08-Th') return '-';
    try {
      const birthDate = new Date(dob);
      if (Number.isNaN(birthDate.getTime())) return '-';
      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      if (
        monthDiff < 0 ||
        (monthDiff === 0 && today.getDate() < birthDate.getDate())
      ) {
        age--;
      }
      return age;
    } catch {
      return '-';
    }
  };

  // Map previous consultations to table rows
  const consultations = useMemo<ConsultationRow[]>(() => {
    if (!patientProfile?.previousConsultations) return [];

    return patientProfile.previousConsultations.map((consultation, index) => {
      // Fallback for department if missing at top level
      const department =
        consultation.department ||
        consultation.summary?.owner?.department ||
        '-';

      return {
        id: (consultation.employeeId || consultation.doctorId || `consult-${index}`) + `-${index}`,
        date: formatDisplayDate(consultation.date),
        employeeId: consultation.employeeId || '-',
        department: department,
        consultationFee: consultation.consultationFee ?? 0,
      };
    });
  }, [patientProfile]);

  const filteredConsultations = useMemo(() => {
    let filtered: ConsultationRow[] = consultations;

    if (searchText) {
      filtered = filtered.filter((c) =>
        c.employeeId.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    if (fromDate) {
      filtered = filtered.filter((c) => {
        const [day, month, year] = c.date.split('-');
        const consultDate = `${year}-${month}-${day}`;
        return consultDate >= fromDate;
      });
    }
    if (toDate) {
      filtered = filtered.filter((c) => {
        const [day, month, year] = c.date.split('-');
        const consultDate = `${year}-${month}-${day}`;
        return consultDate <= toDate;
      });
    }

    return filtered;
  }, [consultations, searchText, fromDate, toDate]);

  const handleClearFilters = () => {
    setSearchText('');
    setFromDate('');
    setToDate('');
  };

  const calculatedAge = patientDob ? getAge(patientDob) : '-';
  const age =
    patientAge && patientAge.trim() !== '' ? patientAge : calculatedAge;
  const dob = patientDob ? formatDisplayDate(patientDob) : '-';
  const registrationDate = patient?.created_on
    ? formatDisplayDate(patient.created_on)
    : '-';

  return (
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center ${
        isOpen ? '' : 'hidden'
      }`}
      style={{ backgroundColor: 'rgba(0, 0, 0, 0.6)' }}
      onClick={onClose}
    >
      <div
        className='bg-white rounded-lg shadow-xl w-full max-w-5xl max-h-[90vh] overflow-hidden flex flex-col'
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        {/* Header */}
        <div className='bg-white text-black px-6 py-4 flex items-center justify-between border-b border-gray-300'>
          <h2 className='text-xl font-semibold'>Patient Profile</h2>
          <button
            onClick={onClose}
            className='w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors'
          >
            <CloseIcon className='text-black' fontSize='small' />
          </button>
        </div>

        {/* Content */}
        <div className='flex-1 overflow-y-auto p-6'>
          {loading ? (
            <div className='flex justify-center items-center py-12'>
              <div className='text-gray-500'>Loading patient data...</div>
            </div>
          ) : (
            <>
              {/* Patient Information */}
              <div className='flex gap-6 mb-8 border-b pb-4'>
                {/* Patient Details */}
                <div className='flex-1 grid grid-cols-2 gap-x-2 gap-y-3'>
                  <div>
                    <span className='font-medium'>Name : </span>
                    <span className='text-black'>{patientName || '-'}</span>
                  </div>
                  <div>
                    <span className='font-medium'>Organisation : </span>
                    <span className='text-black'>{organizationName}</span>
                  </div>
                  <div>
                    <span className='font-medium'>Age : </span>
                    <span className='text-black'>{age}</span>
                  </div>
                  <div>
                    <span className='font-medium'>Date of Birth : </span>
                    <span className='text-black'>{dob}</span>
                  </div>
                  <div>
                    <span className='font-medium'>ABHA Number : </span>
                    <span className='text-black'>
                      {patientAbha || '123456'}
                    </span>
                  </div>
                  <div>
                    <span className='font-medium'>Registration Date : </span>
                    <span className='text-black'>{registrationDate}</span>
                  </div>
                </div>

                {/* Patient ID */}
                <div className='flex-shrink-0'>
                  <div>
                    <span className='font-medium'>Patient ID : </span>
                    <span className='text-black'>{patientId}</span>
                  </div>
                </div>
              </div>

              <div className='space-y-4'>
                <h3 className='text-lg font-semibold text-gray-900'>
                  Previous Consultations
                </h3>

                <div className='flex flex-wrap items-center gap-4'>
                  <SearchBar
                    className='w-[200px]'
                    value={searchText}
                    onChange={setSearchText}
                    placeholder='Search by doctor ID'
                    size='md'
                  />
                  <div className='flex items-center gap-2'>
                    <span className='text-sm text-gray-600'>Date</span>
                    <DatePicker
                      value={fromDate ? new Date(fromDate) : null}
                      onChange={(date) => {
                        const formatted = date
                          ? `${date.getFullYear()}-${String(
                              date.getMonth() + 1
                            ).padStart(2, '0')}-${String(
                              date.getDate()
                            ).padStart(2, '0')}`
                          : '';
                        setFromDate(formatted);
                      }}
                      placeholder='Start date'
                      className='w-[130px] rounded-lg border border-gray-300 px-2 py-1 text-sm focus:border-transparent focus:ring-2 focus:ring-blue-500'
                    />
                    <span className='text-gray-400'>to</span>
                    <DatePicker
                      value={toDate ? new Date(toDate) : null}
                      onChange={(date) => {
                        const formatted = date
                          ? `${date.getFullYear()}-${String(
                              date.getMonth() + 1
                            ).padStart(2, '0')}-${String(
                              date.getDate()
                            ).padStart(2, '0')}`
                          : '';
                        setToDate(formatted);
                      }}
                      placeholder='End date'
                      className='w-[130px] rounded-lg border border-gray-300 px-2 py-1 text-sm focus:border-transparent focus:ring-2 focus:ring-blue-500'
                    />
                  </div>
                  <button
                    className='text-sm text-gray-700 hover:text-gray-900 cursor-pointer bg-transparent border-0 px-2'
                    onClick={handleClearFilters}
                  >
                    Clear Filter
                  </button>
                </div>

                <div className='bg-white rounded-lg shadow overflow-hidden mt-4'>
                  <div className='overflow-x-auto'>
                    <table className='w-full'>
                      <thead>
                        <tr style={{ backgroundColor: '#02537D' }}>
                          <th
                            style={{ width: '150px' }}
                            className='px-4 py-3 text-left text-sm font-semibold text-white border-r border-white/20'
                          >
                            Data
                          </th>
                          <th
                            style={{ width: '150px' }}
                            className='px-4 py-3 text-left text-sm font-semibold text-white border-r border-white/20'
                          >
                            Doctor ID
                          </th>
                          <th
                            style={{ width: '200px' }}
                            className='px-4 py-3 text-left text-sm font-semibold text-white border-r border-white/20'
                          >
                            Department
                          </th>
                          <th
                            style={{ width: '200px' }}
                            className='px-4 py-3 text-left text-sm font-semibold text-white'
                          >
                            Consultation Fee (₹)
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {profileLoading ? (
                          <tr>
                            <td
                              colSpan={4}
                              className='px-4 py-8 text-center text-gray-500'
                            >
                              <div className='flex justify-center'>
                                Loading...
                              </div>
                            </td>
                          </tr>
                        ) : filteredConsultations.length === 0 ? (
                          <tr>
                            <td
                              colSpan={4}
                              className='px-4 py-8 text-center text-gray-500'
                            >
                              No data found.
                            </td>
                          </tr>
                        ) : (
                          filteredConsultations.map((consultation) => (
                            <tr
                              key={consultation.id}
                              className='border-b border-gray-200 hover:bg-gray-50'
                            >
                              <td className='px-4 py-3 text-sm text-gray-900 border-r border-gray-200'>
                                {consultation.date}
                              </td>
                              <td
                                className={`px-4 py-3 text-sm text-gray-900 border-r border-gray-200 ${!consultation.employeeId ? 'text-center' : ''}`}
                              >
                                {consultation.employeeId || '-'}
                              </td>
                              <td className='px-4 py-3 text-sm text-gray-900 border-r border-gray-200'>
                                {consultation.department}
                              </td>
                              <td className='px-4 py-3 text-sm text-gray-900'>
                                {consultation.consultationFee !== undefined && consultation.consultationFee !== null
                                  ? consultation.consultationFee.toFixed(2)
                                  : '0.00'}
                              </td>
                            </tr>
                          ))
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>

        <div className='border-t border-gray-200 px-6 py-4'>
          <div className='text-center text-sm text-gray-500'>
            <div className='powered-by flex items-center justify-center gap-2'>
              <span>Powered By</span>
              <img
                src='/images/Vector.png'
                alt='Arca Logo'
                className='arca-logo'
                onError={(e) => {
                  const target = e.currentTarget;
                  target.style.display = 'none';
                  const textSpan = target.nextElementSibling as HTMLElement;
                  if (textSpan) {
                    textSpan.classList.remove('hidden');
                  }
                }}
              />
              <span className='arca-logo-text hidden'>ARCA</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PatientProfileModal;
