import {
    PATIENT_PROFILE_ENDPOINT,
    SUBSCRIBERS_ENDPOINT,
} from '../../../constants/api-endpoints';
import api from '../../../services/api';

export type InvoiceParams = {
  organizationId: string;
  search?: string;
  type?: string;
  gender?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
};

export type InvoiceBillItem = {
  no?: number;
  drugName?: string;
  genericName?: string;
  brandName?: string;
  strength?: string | number;
  qty?: string | number;
  cost?: string | number;
};

export type InvoiceLabTest = {
  department?: string;
  testName?: string;
  subTests?: unknown[];
  amount?: number;
};

export type InvoiceDoctorInfo = {
  id?: string;
  name?: string;
  email?: string;
  designation?: string;
  department?: string;
};

export type InvoiceBillDetails = {
  type?: string;
  prescriptionId?: string;
  doctorName?: string;
  doctorEmail?: string;
  prescriptionDate?: string;
  items?: InvoiceBillItem[];
  totalAmount?: number;
  paymentMethod?: string;
  billedBy?: string;
  billedAt?: string;
  consultationFee?: number;
  labTestId?: string;
  tests?: InvoiceLabTest[];
  labTechnician?: string;
  testDate?: string;
};

export type InvoiceItem = {
  date: string;
  type: string;
  patientId: string;
  fullName: string;
  gender: string;
  amount: number;
  modeOfPayment: string;
  transactionId: string;
  doctorName: string;
  paymentId: string;
  status: string;
  rawPayment?: {
    _rid?: string;
    _self?: string;
    _etag?: string;
    _attachments?: string;
    created_by?: string;
    updated_by?: string;
    created_on?: string;
    updated_on?: string;
    id?: string;
    razorpayOrderId?: string;
    razorpayPaymentId?: string;
    razorpaySignature?: string;
    amount?: number;
    currency?: string;
    status?: string;
    paymentType?: string;
    patientId?: string;
    subscriberEmail?: string;
    subscriptionId?: string;
    organizationId?: string;
    description?: string;
    created_by_email?: string;
    created_by_name?: string;
    createdAt?: string;
    verifiedAt?: string | null;
    failureReason?: string | null;
    metadata?: Record<string, unknown>;
    notes?: Record<string, unknown>;
    _ts?: number;
  };
};

export type InvoiceListResponse = {
  invoices: InvoiceItem[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
};

export type InvoiceDetailResponse = {
  _rid?: string;
  _self?: string;
  _etag?: string;
  _attachments?: string;
  created_by?: string;
  updated_by?: string;
  created_on?: string;
  updated_on?: string;
  id: string;
  razorpayOrderId?: string;
  razorpayPaymentId?: string;
  razorpaySignature?: string;
  amount?: number;
  amountInRupees?: number;
  currency?: string;
  status?: string;
  paymentType?: string;
  patientId?: string;
  fullName?: string;
  subscriberEmail?: string;
  subscriptionId?: string;
  organizationId?: string;
  description?: string;
  created_by_email?: string;
  created_by_name?: string;
  createdAt?: string;
  verifiedAt?: string | null;
  failureReason?: string | null;
  metadata?: Record<string, unknown>;
  notes?: Record<string, unknown>;
  _ts?: number;
  patient?: {
    name?: string;
    age?: string;
    dob?: string;
    sex?: string;
    height?: string;
    weight?: string;
    maritalStatus?: string;
    address?: {
      city?: string;
      state?: string;
      country?: string;
    };
    contact?: {
      phone?: string;
      email?: string;
    };
    proof?: {
      type?: string;
      url?: string;
      aadharNumber?: string;
      abhaNumber?: string;
      passportNumber?: string;
      panNumber?: string;
    };
    insurance?: {
      provider?: string;
      url?: string;
    };
    id?: string;
    created_by?: string;
    updated_by?: string;
    organizationId?: string;
    created_on?: string;
    updated_on?: string;
    _rid?: string;
    _self?: string;
    _etag?: string;
    _attachments?: string;
    _ts?: number;
  };
  doctor?: InvoiceDoctorInfo | null;
  billDetails?: InvoiceBillDetails | null;
};

export type PreviousConsultation = {
  id?: string;
  date: string;
  doctorId: string;
  doctorName?: string;
  employeeId?: string;
  department?: string;
  consultationFee?: number;
  summary?: {
    owner?: {
      department?: string;
      [key: string]: any;
    };
    [key: string]: any;
  };
  [key: string]: any;
};

export type PatientProfileData = {
  id: string;
  name: string;
  age: string;
  dateOfBirth: string;
  abhaNumber: string;
  organization: string;
  organizationId: string;
  registrationDate: string;
  gender: string;
  phone: string;
  email: string;
};

export type PatientProfileResponse = {
  patient: PatientProfileData;
  previousConsultations: PreviousConsultation[];
};

export type FetchPatientProfileParams = {
  patientId: string;
  organizationId: string;
  startDate?: string;
  endDate?: string;
  doctorId?: string;
};

const buildParams = (params: InvoiceParams) => {
  const query: Record<string, string | number> = {
    organizationId: params.organizationId,
  };

  if (params.page !== undefined) {
    query.page = params.page;
  }
  if (params.limit !== undefined) {
    query.limit = params.limit;
  }

  if (params.search) {
    query.search = params.search.trim();
  }

  if (params.type) {
    query.type = params.type;
  }

  if (params.gender) {
    query.gender = params.gender;
  }
  if (params.startDate) {
    query.startDate = params.startDate;
  }
  if (params.endDate) {
    query.endDate = params.endDate;
  }

  return query;
};

export type FetchInvoiceByIdParams = {
  organizationId: string;
  id: string;
};

const invoiceService = {
  async fetchInvoices(params: InvoiceParams) {
    const endpoint = SUBSCRIBERS_ENDPOINT.replace('/subscriber', '/invoice');
    const response = await api.get(endpoint, {
      params: buildParams(params),
    });
    return response.data as InvoiceListResponse;
  },

  async fetchInvoiceById(params: FetchInvoiceByIdParams) {
    const endpoint = SUBSCRIBERS_ENDPOINT.replace('/subscriber', '/invoice');
    const response = await api.get(endpoint, {
      params: {
        organizationId: params.organizationId,
        id: params.id,
      },
    });
    return response.data as InvoiceDetailResponse;
  },

  async fetchPatientProfile(params: FetchPatientProfileParams) {
    const queryParams: Record<string, string> = {
      patientId: params.patientId,
      organizationId: params.organizationId,
    };

    if (params.startDate) {
      queryParams.startDate = params.startDate;
    }
    if (params.endDate) {
      queryParams.endDate = params.endDate;
    }
    if (params.doctorId) {
      queryParams.doctorId = params.doctorId;
    }

    const response = await api.get(PATIENT_PROFILE_ENDPOINT, {
      params: queryParams,
    });
    return response.data as PatientProfileResponse;
  },
};

export default invoiceService;
