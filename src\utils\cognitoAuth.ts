/**
 * Amazon Cognito Authentication Utilities
 * This file provides authentication functions for AWS Cognito using PKCE flow
 */

import {
    cognitoConfig,
    cognitoEndpoints,
    cognitoStorageKeys,
} from './cognitoConfig';

// ==================== PKCE Helper Functions ====================

/**
 * Generate a cryptographically random string
 */
function generateRandom(length: number): string {
  const array = new Uint8Array(length);
  crypto.getRandomValues(array);
  return btoa(String.fromCharCode(...array))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

/**
 * Create SHA256 hash for PKCE challenge
 */
async function sha256(str: string): Promise<string> {
  const buffer = new TextEncoder().encode(str);
  const hash = await crypto.subtle.digest('SHA-256', buffer);
  return btoa(String.fromCharCode(...new Uint8Array(hash)))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

// ==================== Token Management ====================

/**
 * Check if the access token is expired
 * @param bufferMinutes Number of minutes before actual expiry to consider it expired
 */
export function isTokenExpired(bufferMinutes: number = 5): boolean {
  const expiry = sessionStorage.getItem(cognitoStorageKeys.tokenExpiry);
  if (!expiry) return true;

  const expiryTime = parseInt(expiry, 10);
  const currentTime = Date.now();

  // Consider token expired if it expires in less than bufferMinutes
  return currentTime >= expiryTime - bufferMinutes * 60 * 1000;
}

/**
 * Get the stored access token
 */
export function getCognitoAccessToken(): string | null {
  if (isTokenExpired()) {
    return null;
  }
  return sessionStorage.getItem(cognitoStorageKeys.accessToken);
}

/**
 * Get the stored ID token
 */
export function getCognitoIdToken(): string | null {
  return sessionStorage.getItem(cognitoStorageKeys.idToken);
}

/**
 * Store tokens in session storage
 */
function storeTokens(tokens: {
  access_token: string;
  id_token: string;
  refresh_token?: string;
  expires_in: number;
}): void {
  sessionStorage.setItem(cognitoStorageKeys.accessToken, tokens.access_token);
  sessionStorage.setItem(cognitoStorageKeys.idToken, tokens.id_token);

  if (tokens.refresh_token) {
    sessionStorage.setItem(
      cognitoStorageKeys.refreshToken,
      tokens.refresh_token
    );
  }

  // Store token expiry time (current time + expires_in seconds)
  const expiryTime = Date.now() + tokens.expires_in * 1000;
  sessionStorage.setItem(cognitoStorageKeys.tokenExpiry, expiryTime.toString());

  // Also store in localStorage for persistence across tabs
  localStorage.setItem('token', tokens.access_token);

  // Mark that we're using Cognito as the auth provider
  sessionStorage.setItem(cognitoStorageKeys.authProvider, 'cognito');
}

/**
 * Clear all Cognito tokens and user info
 */
export function clearCognitoTokens(): void {
  sessionStorage.removeItem(cognitoStorageKeys.accessToken);
  sessionStorage.removeItem(cognitoStorageKeys.idToken);
  sessionStorage.removeItem(cognitoStorageKeys.refreshToken);
  sessionStorage.removeItem(cognitoStorageKeys.tokenExpiry);
  sessionStorage.removeItem(cognitoStorageKeys.userInfo);
  sessionStorage.removeItem(cognitoStorageKeys.codeVerifier);
  sessionStorage.removeItem(cognitoStorageKeys.state);
  sessionStorage.removeItem(cognitoStorageKeys.authProvider);
  localStorage.removeItem('token');
  localStorage.removeItem('emrUserInfo');
  localStorage.removeItem('userRole');
  localStorage.removeItem('user'); // Clear user data stored by auth handlers
}

// ==================== Authentication Flow ====================

/**
 * Initiate Cognito login with PKCE flow
 */
export async function cognitoLogin(): Promise<void> {
  try {
    // Generate PKCE parameters
    const verifier = generateRandom(32);
    const challenge = await sha256(verifier);
    const state = generateRandom(16);

    // Save for later use in callback
    sessionStorage.setItem(cognitoStorageKeys.codeVerifier, verifier);
    sessionStorage.setItem(cognitoStorageKeys.state, state);

    // Build authorization URL
    const params = new URLSearchParams({
      response_type: cognitoConfig.responseType,
      client_id: cognitoConfig.clientId,
      redirect_uri: cognitoConfig.redirectUri,
      scope: cognitoConfig.scopes.join(' '),
      state,
      code_challenge: challenge,
      code_challenge_method: cognitoConfig.codeChallengeMethod,
    });

    // Redirect to Cognito login page
    window.location.href = `${cognitoEndpoints.authorize}?${params}`;
  } catch (error) {
    console.error('Cognito login failed:', error);
    throw error;
  }
}

/**
 * Handle the OAuth callback after Cognito redirect
 * Call this function on page load to check for OAuth callback
 */
export async function handleCognitoCallback(): Promise<boolean> {
  const params = new URLSearchParams(window.location.search);
  const code = params.get('code');
  const state = params.get('state');
  const error = params.get('error');

  // Check if this is an OAuth callback
  if (!code && !error) {
    return false; // Not a callback
  }

  // Handle error from Cognito
  if (error) {
    const errorDescription = params.get('error_description') || 'Unknown error';
    console.error('Cognito OAuth error:', error, errorDescription);

    // Clean up URL
    window.history.replaceState({}, document.title, window.location.pathname);
    throw new Error(`Cognito authentication failed: ${errorDescription}`);
  }

  // Verify state to prevent CSRF attacks
  const savedState = sessionStorage.getItem(cognitoStorageKeys.state);
  if (state !== savedState) {
    console.error('State mismatch - possible CSRF attack');
    window.history.replaceState({}, document.title, window.location.pathname);
    throw new Error('State mismatch - authentication failed');
  }

  // Exchange authorization code for tokens
  const verifier = sessionStorage.getItem(cognitoStorageKeys.codeVerifier);
  if (!verifier) {
    console.error('No code verifier found');
    window.history.replaceState({}, document.title, window.location.pathname);
    throw new Error('No code verifier found');
  }

  if (!code) {
    console.error('No authorization code found');
    window.history.replaceState({}, document.title, window.location.pathname);
    throw new Error('No authorization code found');
  }

  try {
    const tokenResponse = await exchangeCodeForTokens(code, verifier);
    storeTokens(tokenResponse);

    // Fetch and store user info
    await fetchAndStoreUserInfo(tokenResponse.access_token);

    // Clean up URL
    window.history.replaceState({}, document.title, window.location.pathname);

    // Clean up temporary storage
    sessionStorage.removeItem(cognitoStorageKeys.codeVerifier);
    sessionStorage.removeItem(cognitoStorageKeys.state);

    return true; // Successfully handled callback
  } catch (error) {
    console.error('Token exchange failed:', error);
    window.history.replaceState({}, document.title, window.location.pathname);
    throw error;
  }
}

/**
 * Exchange authorization code for tokens
 */
async function exchangeCodeForTokens(
  code: string,
  verifier: string
): Promise<{
  access_token: string;
  id_token: string;
  refresh_token?: string;
  expires_in: number;
  token_type: string;
}> {
  const body = new URLSearchParams({
    grant_type: 'authorization_code',
    client_id: cognitoConfig.clientId,
    code,
    redirect_uri: cognitoConfig.redirectUri,
    code_verifier: verifier,
  });

  const response = await fetch(cognitoEndpoints.token, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body,
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error('Token exchange failed:', errorText);
    throw new Error(`Token exchange failed: ${errorText}`);
  }

  return await response.json();
}

/**
 * Fetch user info from Cognito and store it
 */
async function fetchAndStoreUserInfo(accessToken: string): Promise<void> {
  try {
    const response = await fetch(cognitoEndpoints.userInfo, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch user info');
    }

    const userInfo = await response.json();
    sessionStorage.setItem(
      cognitoStorageKeys.userInfo,
      JSON.stringify(userInfo)
    );
  } catch (error) {
    console.error('Failed to fetch user info:', error);
    // Don't throw - user info is optional
  }
}

/**
 * Get stored Cognito user info
 */
export function getCognitoUserInfo(): any | null {
  const userInfoStr = sessionStorage.getItem(cognitoStorageKeys.userInfo);
  if (!userInfoStr) return null;

  try {
    return JSON.parse(userInfoStr);
  } catch (error) {
    console.error('Failed to parse user info:', error);
    return null;
  }
}

/**
 * Logout from Cognito
 */
export async function cognitoLogout(): Promise<void> {
  try {
    // Clear local tokens first
    clearCognitoTokens();

    // Build logout URL with properly encoded parameters
    // Note: The logout_uri MUST be registered in AWS Cognito App Client settings
    const logoutUri = encodeURIComponent(cognitoConfig.postLogoutRedirectUri);
    const logoutUrl = `${cognitoEndpoints.logout}?client_id=${cognitoConfig.clientId}&logout_uri=${logoutUri}`;

    console.log('Cognito logout URL:', logoutUrl);

    // Redirect to Cognito logout
    window.location.href = logoutUrl;
  } catch (error) {
    console.error('Cognito logout failed:', error);
    // Even if logout fails, redirect to login page
    window.location.href = '/login';
  }
}

/**
 * Check if user is authenticated with Cognito
 */
export function isCognitoAuthenticated(): boolean {
  const accessToken = getCognitoAccessToken();
  const authProvider = sessionStorage.getItem(cognitoStorageKeys.authProvider);
  return !!accessToken && authProvider === 'cognito';
}

/**
 * Refresh the access token using refresh token
 */
export async function refreshCognitoToken(): Promise<boolean> {
  const refreshToken = sessionStorage.getItem(cognitoStorageKeys.refreshToken);

  if (!refreshToken) {
    console.warn('No refresh token available');
    return false;
  }

  try {
    const body = new URLSearchParams({
      grant_type: 'refresh_token',
      client_id: cognitoConfig.clientId,
      refresh_token: refreshToken,
    });

    const response = await fetch(cognitoEndpoints.token, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body,
    });

    if (!response.ok) {
      throw new Error('Token refresh failed');
    }

    const tokens = await response.json();
    storeTokens({
      ...tokens,
      refresh_token: refreshToken, // Keep the existing refresh token
    });

    return true;
  } catch (error) {
    console.error('Failed to refresh token:', error);
    clearCognitoTokens();
    return false;
  }
}

/**
 * Setup a timer to proactively refresh the token before it expires
 * @returns A function to clear the timer
 */
export function setupTokenRefresh(): () => void {
  let refreshTimer: NodeJS.Timeout | null = null;

  const scheduleNextRefresh = () => {
    // If there's an existing timer, clear it
    if (refreshTimer) {
      clearTimeout(refreshTimer);
    }

    const expiry = sessionStorage.getItem(cognitoStorageKeys.tokenExpiry);
    if (!expiry) return;

    const expiryTime = parseInt(expiry, 10);
    const currentTime = Date.now();

    // Calculate time until expiry minus 5 minutes buffer
    const bufferMs = 5 * 60 * 1000;
    const timeUntilRefresh = expiryTime - currentTime - bufferMs;

    // If already expired or expires very soon, refresh now
    if (timeUntilRefresh <= 0) {
      console.log('setupTokenRefresh: Token expires soon or already expired, refreshing now');
      refreshCognitoToken().then(success => {
        if (success) {
          scheduleNextRefresh();
        }
      });
      return;
    }

    // Schedule next refresh
    console.log(`setupTokenRefresh: Scheduling next refresh in ${Math.round(timeUntilRefresh / 1000 / 60)} minutes`);
    refreshTimer = setTimeout(async () => {
      console.log('setupTokenRefresh: Proactively refreshing token...');
      const success = await refreshCognitoToken();
      if (success) {
        scheduleNextRefresh();
      } else {
        console.error('setupTokenRefresh: Proactive refresh failed');
      }
    }, timeUntilRefresh);
  };

  scheduleNextRefresh();

  return () => {
    if (refreshTimer) {
      clearTimeout(refreshTimer);
    }
  };
}
