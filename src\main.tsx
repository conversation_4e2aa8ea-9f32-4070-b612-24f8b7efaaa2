import './index.css';

import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { BrowserRouter } from 'react-router-dom';

import App from './App.tsx';
import { initializeMonitoring } from './utils/monitoring';
import { migrateUserDataToSessionStorage } from './utils/storageUtils';

// Import storage test utilities in development
if (import.meta.env.DEV) {
  import('./utils/storageTestUtils');
}

// Initialize monitoring
initializeMonitoring();

// Migrate user data from localStorage to sessionStorage
migrateUserDataToSessionStorage();

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <BrowserRouter>
      <App />
    </BrowserRouter>
  </StrictMode>
);
