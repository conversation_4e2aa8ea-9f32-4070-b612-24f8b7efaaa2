# Multi-Tab User Isolation Implementation

## Problem Statement

The application was storing user-specific data in `localStorage`, which is shared across all browser tabs. This caused issues when different users logged in to different tabs, as user data would persist and interfere with each other.

## Solution Overview

We've implemented a comprehensive solution that moves user-specific data to `sessionStorage` (tab-specific) while keeping application-wide settings in `localStorage` (cross-tab persistent).

## Storage Strategy

### SessionStorage (Tab-Specific) 🔒
**Used for user-specific data that should NOT be shared across tabs:**

- `user` - User profile data
- `emrUserInfo` - EMR user information  
- `userRole` - User role
- All Cognito authentication tokens (already implemented)

### LocalStorage (Cross-Tab Persistent) 🌐
**Used for application-wide settings that should persist across tabs:**

- `selectedOrganization` - Selected organization (for super admins only)
- Future: Theme preferences, language settings, etc.

## Key Changes Made

### 1. Storage Utilities (`src/utils/storageUtils.ts`)
- Created centralized storage management utilities
- Provides clear separation between sessionStorage and localStorage usage
- Includes migration utilities to move existing data

### 2. Authentication Updates
- **CognitoAuthHandler**: Now stores user data in sessionStorage
- **MsalAuthHandler**: Updated to use sessionStorage for user data
- **Auth Services**: Modified to use sessionStorage for caching user info
- **Token Storage**: Removed localStorage token storage (tokens are now tab-specific)

### 3. Storage Access Functions
- **auth-utils.ts**: Updated `getCurrentUser()` with migration fallback
- **API Service**: Updated 401 error handling to clear both storages
- **Auth Slice**: Modified logout to preserve app settings while clearing user data

### 4. Component Updates
- **LandingPage**: Uses sessionStorage for user data persistence
- **SubscriptionList**: Updated to check sessionStorage first, then localStorage
- All authentication handlers updated to use new storage strategy

### 5. Migration & Testing
- **Automatic Migration**: Existing localStorage user data is automatically migrated to sessionStorage on app startup
- **Test Utilities**: Added comprehensive testing tools for verifying multi-tab isolation

## Testing the Implementation

### Browser Console Testing

The application includes built-in testing utilities available in development mode:

```javascript
// Check current storage state
window.storageTest.logStorageState();

// Test multi-tab isolation
window.storageTest.testMultiTabIsolation();

// Clear user data for testing
window.storageTest.clearUserDataForTesting();

// Get current storage state
const state = window.storageTest.getCurrentStorageState();
```

### Manual Testing Steps

1. **Login with User A** in Tab 1
2. **Open new tab** (Tab 2) 
3. **Login with User B** in Tab 2
4. **Verify isolation**:
   - Tab 1 should still show User A's data
   - Tab 2 should show User B's data
   - Both tabs should share the same `selectedOrganization` (for super admins)

### Expected Behavior

✅ **User data is isolated per tab**  
✅ **Different users can login in different tabs**  
✅ **No data interference between tabs**  
✅ **Application settings persist across tabs**  
✅ **Automatic migration of existing data**  

## Benefits

1. **Multi-User Support**: Different users can work in different tabs simultaneously
2. **Data Isolation**: User-specific data doesn't leak between tabs
3. **Improved Security**: Tokens and user data are tab-specific
4. **Better UX**: Users won't see other users' data unexpectedly
5. **Backward Compatibility**: Automatic migration ensures smooth transition

## Migration Process

The migration happens automatically when the application starts:

1. **Check sessionStorage** for user data first
2. **Fallback to localStorage** if not found in sessionStorage
3. **Migrate data** from localStorage to sessionStorage if found
4. **Remove from localStorage** after successful migration
5. **Log migration** for debugging purposes

## File Structure

```
src/
├── utils/
│   ├── storageUtils.ts          # Storage management utilities
│   ├── storageTestUtils.ts      # Testing utilities
│   └── cognitoAuth.ts           # Updated token storage
├── components/Auth/
│   ├── CognitoAuthHandler.tsx   # Updated for sessionStorage
│   └── MsalAuthHandler.tsx      # Updated for sessionStorage
├── store/features/auth/
│   ├── auth.slice.ts            # Updated logout logic
│   ├── auth.service.ts          # Updated storage usage
│   └── cognitoAuth.service.ts   # Updated caching logic
└── docs/
    └── MULTI_TAB_USER_ISOLATION.md  # This documentation
```

## Future Considerations

1. **Theme Preferences**: Can be added to localStorage for cross-tab persistence
2. **Language Settings**: Should use localStorage for consistent experience
3. **User Preferences**: Evaluate per-setting whether it should be tab-specific or cross-tab
4. **Session Management**: Consider implementing session timeout per tab

## Troubleshooting

If you encounter issues:

1. **Clear browser storage** completely and test fresh login
2. **Check console logs** for migration messages
3. **Use test utilities** to verify storage state
4. **Verify token expiry** isn't causing authentication issues

The implementation provides a robust foundation for multi-tab user isolation while maintaining backward compatibility and ease of testing.
