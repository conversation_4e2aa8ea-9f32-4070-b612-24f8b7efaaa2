/**
 * Storage Test Utilities
 * Utilities for testing multi-tab user isolation
 */

export interface StorageTestInfo {
  tabId: string;
  timestamp: number;
  user: any;
  emrUserInfo: any;
  userRole: string | null;
  selectedOrganization: any;
}

/**
 * Generate a unique tab ID for testing
 */
export const generateTabId = (): string => {
  return `tab_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Get current storage state for testing
 */
export const getCurrentStorageState = (): StorageTestInfo => {
  const tabId = generateTabId();
  
  return {
    tabId,
    timestamp: Date.now(),
    user: getStorageItem('user', 'session'),
    emrUserInfo: getStorageItem('emrUserInfo', 'session'),
    userRole: getStorageItem('userRole', 'session'),
    selectedOrganization: getStorageItem('selectedOrganization', 'local'),
  };
};

/**
 * Helper to get item from specific storage
 */
const getStorageItem = (key: string, storageType: 'local' | 'session'): any => {
  try {
    const storage = storageType === 'local' ? localStorage : sessionStorage;
    const item = storage.getItem(key);
    return item ? JSON.parse(item) : null;
  } catch (error) {
    console.error(`Failed to get ${key} from ${storageType}Storage:`, error);
    return null;
  }
};

/**
 * Log current storage state to console
 */
export const logStorageState = (label: string = 'Storage State'): void => {
  const state = getCurrentStorageState();
  
  console.group(`🔍 ${label} - ${new Date().toLocaleTimeString()}`);
  console.log('Tab ID:', state.tabId);
  console.log('User (sessionStorage):', state.user?.email || 'None');
  console.log('EMR User Info (sessionStorage):', state.emrUserInfo?.email || 'None');
  console.log('User Role (sessionStorage):', state.userRole || 'None');
  console.log('Selected Organization (localStorage):', state.selectedOrganization?.name || 'None');
  console.groupEnd();
};

/**
 * Compare storage states between tabs
 */
export const compareStorageStates = (state1: StorageTestInfo, state2: StorageTestInfo): void => {
  console.group('🔄 Storage State Comparison');
  
  console.log('Tab 1 User:', state1.user?.email || 'None');
  console.log('Tab 2 User:', state2.user?.email || 'None');
  console.log('Users Match:', state1.user?.email === state2.user?.email);
  
  console.log('Tab 1 Role:', state1.userRole || 'None');
  console.log('Tab 2 Role:', state2.userRole || 'None');
  console.log('Roles Match:', state1.userRole === state2.userRole);
  
  console.log('Tab 1 Org:', state1.selectedOrganization?.name || 'None');
  console.log('Tab 2 Org:', state2.selectedOrganization?.name || 'None');
  console.log('Organizations Match:', state1.selectedOrganization?.name === state2.selectedOrganization?.name);
  
  console.groupEnd();
};

/**
 * Test multi-tab isolation
 */
export const testMultiTabIsolation = (): void => {
  console.group('🧪 Multi-Tab Isolation Test');
  
  // Log current state
  logStorageState('Current Tab State');
  
  // Instructions for manual testing
  console.log('📋 Manual Testing Instructions:');
  console.log('1. Open this application in another tab');
  console.log('2. Login with a different user in the new tab');
  console.log('3. Run window.storageTest.logStorageState("Other Tab") in the other tab');
  console.log('4. Compare the results - user data should be different, but selectedOrganization should be the same for super admins');
  
  console.groupEnd();
};

/**
 * Clear all user data for testing
 */
export const clearUserDataForTesting = (): void => {
  console.log('🧹 Clearing user data for testing...');
  
  // Clear sessionStorage user data
  sessionStorage.removeItem('user');
  sessionStorage.removeItem('emrUserInfo');
  sessionStorage.removeItem('userRole');
  
  // Clear Cognito tokens
  const cognitoKeys = [
    'cognito.accessToken',
    'cognito.idToken', 
    'cognito.refreshToken',
    'cognito.tokenExpiry',
    'cognito.userInfo',
    'cognito.authProvider'
  ];
  
  cognitoKeys.forEach(key => sessionStorage.removeItem(key));
  
  // Clear legacy localStorage items
  localStorage.removeItem('user');
  localStorage.removeItem('emrUserInfo');
  localStorage.removeItem('userRole');
  localStorage.removeItem('token');
  
  console.log('✅ User data cleared');
  logStorageState('After Clearing');
};

// Make utilities available globally for testing
declare global {
  interface Window {
    storageTest: {
      logStorageState: typeof logStorageState;
      getCurrentStorageState: typeof getCurrentStorageState;
      testMultiTabIsolation: typeof testMultiTabIsolation;
      clearUserDataForTesting: typeof clearUserDataForTesting;
      compareStorageStates: typeof compareStorageStates;
    };
  }
}

// Expose utilities globally in development
if (import.meta.env.DEV) {
  window.storageTest = {
    logStorageState,
    getCurrentStorageState,
    testMultiTabIsolation,
    clearUserDataForTesting,
    compareStorageStates,
  };
  
  console.log('🔧 Storage test utilities available at window.storageTest');
}
